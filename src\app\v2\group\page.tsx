import Image from 'next/image';

import Footer from '@/components/v2/Footer';
import BaseLayout from '@/components/v2/layouts/BaseLayout';
import { groupData } from '@/db/v2/group';

export default function V2GroupPage() {
  return (
    <>
      <BaseLayout headerType="header4" contentType="content4">
        <h2>Group photos</h2>
        <p align="center">
          {groupData.photos.map((photo) => (
            <span key={photo.id}>
              <Image id="imgleft" src={photo.src} width={490} height={366} alt={photo.description || photo.date} />
              <br />
              {photo.date}
              <br />
              <br />
            </span>
          ))}
        </p>

        <h2>Current members</h2>
        {groupData.currentMembers.map((member) => (
          <p key={member.id}>
            <a name={member.id}></a>
            <h4>{member.name}</h4>
            {member.photo && (
              <Image
                id={member.isPI ? 'imgright' : 'imgleft'}
                src={member.photo}
                width={member.isPI ? 300 : 230}
                height={member.isPI ? 300 : 230}
                alt={member.name}
              />
            )}
            {member.cv && <a href={member.cv}>CURRICULUM VITAE</a>}
            <br />
            {member.education}
            <br />
            <br />
            <b>Position: </b>
            {member.position}
            <br />
            {member.researchInterests && (
              <>
                <b>Research interests: </b>
                {member.researchInterests}
                <br />
              </>
            )}
            {member.hobbies && (
              <>
                <b>Hobbies: </b>
                {member.hobbies}
                <br />
              </>
            )}
            {member.office && (
              <>
                <b>Office: </b>
                {member.office}
                <br />
              </>
            )}
            {member.email && (
              <>
                <b>Email: </b>
                <a href={`mailto:${member.email}`}>{member.email}</a>
                <br />
              </>
            )}
            <br />
            <p align="right">
              <a href="#top">top &uarr;</a>
            </p>
            <h5> </h5>
          </p>
        ))}
      </BaseLayout>
      <br />
      <Footer />
    </>
  );
}
